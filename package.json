{"name": "raybox-ui", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "lint": "eslint"}, "dependencies": {"@fontsource-variable/geist": "^5.2.6", "@fontsource-variable/geist-mono": "^5.2.6", "@hookform/resolvers": "^5.2.1", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-slot": "^1.2.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.540.0", "next": "15.5.0", "next-intl": "^4.3.5", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.62.0", "react-qr-code": "^2.0.18", "tailwind-merge": "^3.3.1", "zod": "^4.0.17"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.5.0", "tailwindcss": "^4", "tw-animate-css": "^1.3.7", "typescript": "^5"}}