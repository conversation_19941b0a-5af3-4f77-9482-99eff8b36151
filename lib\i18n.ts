import { Locale } from '@/i18n';
import zhMessages from '@/messages/zh.json';
import enMessages from '@/messages/en.json';

const messages = {
  zh: zhMessages,
  en: enMessages
};

export function getMessages(locale: Locale) {
  return messages[locale] || messages.zh;
}

export function t(key: string, locale: Locale = 'zh') {
  const msgs = getMessages(locale);
  const keys = key.split('.');
  let value: Record<string, unknown> | unknown = msgs;
  
  for (const k of keys) {
    if (value && typeof value === 'object' && k in value) {
      value = (value as Record<string, unknown>)[k];
    } else {
      return key;
    }
  }

  return typeof value === 'string' ? value : key;
}
