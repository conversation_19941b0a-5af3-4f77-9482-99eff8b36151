'use client';

import Link from "next/link";
import { useTranslations, useLocale } from '@/components/i18n-provider';
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Settings, Shield, Smartphone, Zap } from "lucide-react";
import { Navigation } from "@/components/navigation";

export default function Home() {
  const t = useTranslations();
  const locale = useLocale();

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <Navigation />

      {/* Hero Section */}
      <main className="container mx-auto px-4 py-16">
        <div className="text-center mb-20">
          <h2 className="text-4xl font-bold text-foreground mb-6">
            {t('home.title')}
          </h2>
          <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
            {t('home.subtitle')}
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href={`/${locale}/config`}>
              <Button size="lg" className="w-full sm:w-auto">
                {t('common.startConfig')}
              </Button>
            </Link>
            <Button variant="outline" size="lg" className="w-full sm:w-auto">
              {t('common.learnMore')}
            </Button>
          </div>
        </div>

        {/* Features */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-20">
          <Card className="text-center border-border">
            <CardHeader>
              <div className="w-12 h-12 bg-muted rounded-lg flex items-center justify-center mx-auto mb-4">
                <Settings className="w-6 h-6 text-muted-foreground" />
              </div>
              <CardTitle className="text-foreground">{t('home.features.unified.title')}</CardTitle>
            </CardHeader>
            <CardContent>
              <CardDescription className="text-muted-foreground">
                {t('home.features.unified.description')}
              </CardDescription>
            </CardContent>
          </Card>

          <Card className="text-center border-border">
            <CardHeader>
              <div className="w-12 h-12 bg-muted rounded-lg flex items-center justify-center mx-auto mb-4">
                <Smartphone className="w-6 h-6 text-muted-foreground" />
              </div>
              <CardTitle className="text-foreground">{t('home.features.mobile.title')}</CardTitle>
            </CardHeader>
            <CardContent>
              <CardDescription className="text-muted-foreground">
                {t('home.features.mobile.description')}
              </CardDescription>
            </CardContent>
          </Card>

          <Card className="text-center border-border">
            <CardHeader>
              <div className="w-12 h-12 bg-muted rounded-lg flex items-center justify-center mx-auto mb-4">
                <Zap className="w-6 h-6 text-muted-foreground" />
              </div>
              <CardTitle className="text-foreground">{t('home.features.quick.title')}</CardTitle>
            </CardHeader>
            <CardContent>
              <CardDescription className="text-muted-foreground">
                {t('home.features.quick.description')}
              </CardDescription>
            </CardContent>
          </Card>

          <Card className="text-center border-border">
            <CardHeader>
              <div className="w-12 h-12 bg-muted rounded-lg flex items-center justify-center mx-auto mb-4">
                <Shield className="w-6 h-6 text-muted-foreground" />
              </div>
              <CardTitle className="text-foreground">{t('home.features.secure.title')}</CardTitle>
            </CardHeader>
            <CardContent>
              <CardDescription className="text-muted-foreground">
                {t('home.features.secure.description')}
              </CardDescription>
            </CardContent>
          </Card>
        </div>


      </main>

      {/* Footer */}
      <footer className="bg-background border-t border-border mt-20">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center text-muted-foreground">
            <p>{t('footer.copyright')}</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
