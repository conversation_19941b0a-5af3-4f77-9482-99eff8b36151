"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { UIConfig, SUIConfig, XUIConfig, ThreeXUIConfig } from "@/lib/types";
import { parseUrl } from "@/lib/url-utils";
import { ConfigPreview } from "@/components/config-preview";
import { Navigation } from "@/components/navigation";
import { PageHeader } from "@/components/page-header";
import { useTranslations } from '@/components/i18n-provider';

export default function ConfigPage() {
  const t = useTranslations();
  const [configType, setConfigType] = useState<"s-ui" | "x-ui" | "3x-ui">("s-ui");
  const [formData, setFormData] = useState({
    name: "",
    url: "",
    username: "",
    password: "",
    api: "",
    certFingerprints: "",
  });
  const [generatedConfig, setGeneratedConfig] = useState<UIConfig | null>(null);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: "" }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = t('config.errors.nameRequired');
    }

    if (!formData.url.trim()) {
      newErrors.url = t('config.errors.urlRequired');
    } else {
      try {
        parseUrl(formData.url);
      } catch (error) {
        newErrors.url = error instanceof Error ? error.message : t('config.errors.urlInvalid');
      }
    }

    if (configType !== "s-ui") {
      if (!formData.username.trim()) {
        newErrors.username = t('config.errors.usernameRequired');
      }
      if (!formData.password.trim()) {
        newErrors.password = t('config.errors.passwordRequired');
      }
    }

    if (configType === "s-ui" && !formData.api.trim()) {
      newErrors.api = t('config.errors.apiKeyRequired');
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const generateConfig = () => {
    if (!validateForm()) return;

    try {
      const parsedUrl = parseUrl(formData.url);
      const certFingerprints = formData.certFingerprints
        .split('\n')
        .map(fp => fp.trim())
        .filter(fp => fp.length > 0);

      const baseConfig = {
        name: formData.name,
        url: formData.url.replace(/^https?:\/\//, ""),
        protocol: parsedUrl.protocol,
        certFingerprints: certFingerprints.length > 0 ? certFingerprints : undefined,
      };

      let config: UIConfig;

      switch (configType) {
        case "s-ui":
          config = {
            ...baseConfig,
            type: "s-ui",
            api: formData.api,
          } as SUIConfig;
          break;
        case "x-ui":
          config = {
            ...baseConfig,
            type: "x-ui",
            username: formData.username,
            password: formData.password,
          } as XUIConfig;
          break;
        case "3x-ui":
          config = {
            ...baseConfig,
            type: "3x-ui",
            username: formData.username,
            password: formData.password,
          } as ThreeXUIConfig;
          break;
      }

      setGeneratedConfig(config);
    } catch (error) {
      setErrors({ url: error instanceof Error ? error.message : t('config.errors.configGenerationFailed') });
    }
  };

  const downloadConfig = () => {
    if (!generatedConfig) return;

    const dataStr = JSON.stringify(generatedConfig, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${generatedConfig.name}.json`;
    link.click();
    URL.revokeObjectURL(url);
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <Navigation />
      <PageHeader
        title={t('config.title')}
        description={t('config.description')}
      />

      {/* Content */}
      <main className="container mx-auto px-4 py-8 max-w-4xl">
        <div className="grid lg:grid-cols-2 gap-8">
          {/* Configuration Form */}
          <Card>
            <CardHeader>
              <CardTitle>{t('config.form.createConfig')}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Panel Type Selection */}
              <div className="space-y-2">
                <Label htmlFor="type">{t('config.form.panelType')}</Label>
                <Select value={configType} onValueChange={(value: "s-ui" | "x-ui" | "3x-ui") => setConfigType(value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="s-ui">s-ui</SelectItem>
                    <SelectItem value="x-ui">x-ui</SelectItem>
                    <SelectItem value="3x-ui">3x-ui</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Configuration Name */}
              <div className="space-y-2">
                <Label htmlFor="name">{t('config.form.configName')}</Label>
                <Input
                  id="name"
                  placeholder={t('config.form.configNamePlaceholder')}
                  value={formData.name}
                  onChange={(e) => handleInputChange("name", e.target.value)}
                />
                {errors.name && <p className="text-sm text-red-500">{errors.name}</p>}
              </div>

              {/* URL */}
              <div className="space-y-2">
                <Label htmlFor="url">{t('config.form.fullUrl')}</Label>
                <Input
                  id="url"
                  placeholder={t('config.form.fullUrlPlaceholder')}
                  value={formData.url}
                  onChange={(e) => handleInputChange("url", e.target.value)}
                />
                {errors.url && <p className="text-sm text-red-500">{errors.url}</p>}
                <p className="text-xs text-gray-500">{t('config.form.fullUrlHint')}</p>
              </div>

              {/* Username and Password for x-ui and 3x-ui */}
              {configType !== "s-ui" && (
                <>
                  <div className="space-y-2">
                    <Label htmlFor="username">{t('config.form.username')}</Label>
                    <Input
                      id="username"
                      placeholder={t('config.form.usernamePlaceholder')}
                      value={formData.username}
                      onChange={(e) => handleInputChange("username", e.target.value)}
                    />
                    {errors.username && <p className="text-sm text-red-500">{errors.username}</p>}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="password">{t('config.form.password')}</Label>
                    <Input
                      id="password"
                      type="password"
                      placeholder={t('config.form.passwordPlaceholder')}
                      value={formData.password}
                      onChange={(e) => handleInputChange("password", e.target.value)}
                    />
                    {errors.password && <p className="text-sm text-red-500">{errors.password}</p>}
                  </div>
                </>
              )}

              {/* API Key for s-ui */}
              {configType === "s-ui" && (
                <div className="space-y-2">
                  <Label htmlFor="api">{t('config.form.apiKey')}</Label>
                  <Input
                    id="api"
                    placeholder={t('config.form.apiKeyPlaceholder')}
                    value={formData.api}
                    onChange={(e) => handleInputChange("api", e.target.value)}
                  />
                  {errors.api && <p className="text-sm text-red-500">{errors.api}</p>}
                </div>
              )}

              {/* Certificate Fingerprints */}
              <div className="space-y-2">
                <Label htmlFor="certFingerprints">{t('config.form.certFingerprints')}</Label>
                <Textarea
                  id="certFingerprints"
                  placeholder={t('config.form.certFingerprintsPlaceholder')}
                  rows={3}
                  value={formData.certFingerprints}
                  onChange={(e) => handleInputChange("certFingerprints", e.target.value)}
                />
                <p className="text-xs text-gray-500">{t('config.form.certFingerprintsHint')}</p>
              </div>

              <Button onClick={generateConfig} className="w-full">
                {t('config.form.generateConfig')}
              </Button>
            </CardContent>
          </Card>

          {/* Generated Config and QR Code */}
          {generatedConfig && (
            <ConfigPreview
              config={generatedConfig}
              onDownload={downloadConfig}
            />
          )}
        </div>
      </main>
    </div>
  );
}
