import type { Metadata } from "next";
import "./globals.css";
import "@fontsource-variable/geist";
import "@fontsource-variable/geist-mono";

export const metadata: Metadata = {
  title: "RayBoxUI - 节点管理工具",
  description: "集成了x-ui、3x-ui、s-ui的掌上节点管理配置工具",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="zh">
      <body
        className="font-sans antialiased"
      >
        {children}
      </body>
    </html>
  );
}
