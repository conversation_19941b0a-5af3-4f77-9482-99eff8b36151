import { ProtocolType } from './types';

export interface ParsedUrl {
  protocol: ProtocolType;
  host: string;
  port: number;
  path?: string;
}

export function parseUrl(url: string): ParsedUrl {
  try {
    const urlObj = new URL(url);
    
    if (urlObj.protocol !== 'http:' && urlObj.protocol !== 'https:') {
      throw new Error('协议必须是 http 或 https');
    }

    const protocol: ProtocolType = urlObj.protocol === 'https:' ? 'https' : 'http';
    const host = urlObj.hostname;
    const port = urlObj.port ? parseInt(urlObj.port) : (protocol === 'https' ? 443 : 80);
    const path = urlObj.pathname !== '/' ? urlObj.pathname : undefined;

    return {
      protocol,
      host,
      port,
      path
    };
  } catch {
    throw new Error('无效的URL格式');
  }
}

export function buildUrl(protocol: ProtocolType, host: string, port: number, path?: string): string {
  const defaultPort = protocol === 'https' ? 443 : 80;
  const portStr = port !== defaultPort ? `:${port}` : '';
  const pathStr = path || '';
  
  return `${protocol}://${host}${portStr}${pathStr}`;
}
