# RayBoxUI 使用指南

## 概述

RayBoxUI 是一个专为移动端优化的节点管理工具，支持 s-ui、x-ui、3x-ui 三种面板类型。

## 功能特性

### 支持的面板类型

1. **s-ui** - 使用 API 密钥认证
2. **x-ui** - 使用用户名/密码认证  
3. **3x-ui** - 使用用户名/密码认证

### 主要功能

- 📱 移动端优化的响应式设计
- 🔧 简化的配置流程
- 📱 自动生成二维码
- 💾 配置文件下载
- 🔒 证书指纹验证
- 🌙 深色模式支持

## 使用步骤

### 1. 创建配置

1. 访问首页，点击"配置节点"按钮
2. 选择面板类型（s-ui、x-ui、3x-ui）
3. 填写必要信息：

#### 通用字段
- **配置名称**: 为您的配置起一个易识别的名称
- **完整URL**: 包含协议、域名/IP和端口的完整地址
  - 示例: `https://example.com:8080`
  - 示例: `http://*************:54321`

#### s-ui 特有字段
- **API密钥**: s-ui 面板的 API 密钥

#### x-ui / 3x-ui 特有字段
- **用户名**: 面板登录用户名
- **密码**: 面板登录密码

#### 可选字段
- **证书指纹**: SHA256 证书指纹，每行一个，用于验证服务器证书

### 2. 生成配置

点击"生成配置"按钮后，系统会：

1. 验证输入的信息
2. 解析URL并分离协议、主机、端口
3. 生成标准化的JSON配置
4. 显示配置预览和二维码

### 3. 使用配置

生成配置后，您可以：

- **扫描二维码**: 使用支持的客户端扫描二维码导入配置
- **下载配置**: 下载JSON文件到本地
- **复制配置**: 复制JSON内容到剪贴板

## 配置格式说明

### s-ui 配置格式

```json
{
  "type": "s-ui",
  "name": "配置名称",
  "host": "example.com",
  "port": 8080,
  "protocol": "https",
  "api": "your-api-key",
  "certFingerprints": ["sha256:..."]
}
```

### x-ui 配置格式

```json
{
  "type": "x-ui",
  "name": "配置名称", 
  "host": "example.com",
  "port": 8080,
  "protocol": "https",
  "username": "admin",
  "password": "password",
  "certFingerprints": ["sha256:..."]
}
```

### 3x-ui 配置格式

```json
{
  "type": "3x-ui",
  "name": "配置名称",
  "host": "example.com", 
  "port": 8080,
  "protocol": "https",
  "username": "admin",
  "password": "password",
  "certFingerprints": ["sha256:..."]
}
```

## 安全注意事项

### 证书指纹验证

为了确保连接安全，建议配置证书指纹：

1. 获取服务器证书的 SHA256 指纹
2. 在配置表单的"证书指纹"字段中输入
3. 每行一个指纹
4. 客户端会验证服务器证书是否匹配

### 数据隐私

- 所有配置数据仅存储在您的本地设备
- 不会上传到任何服务器
- 敏感信息在界面中可以选择隐藏显示

## 常见问题

### Q: 支持哪些协议？
A: 目前支持 HTTP 和 HTTPS 协议。

### Q: 如何获取证书指纹？
A: 可以使用浏览器查看证书详情，或使用 openssl 命令获取。

### Q: 配置文件可以在哪些客户端使用？
A: 配置文件采用标准JSON格式，可以被支持相应面板的客户端导入使用。

### Q: 忘记密码怎么办？
A: 配置文件中的密码是明文存储的，请妥善保管配置文件。

## 技术支持

如有问题，请通过以下方式联系：

- GitHub Issues
- 应用内反馈功能

## 更新日志

### v0.1.0
- 初始版本发布
- 支持 s-ui、x-ui、3x-ui 三种面板
- 移动端优化
- 二维码生成功能
- 证书指纹验证
