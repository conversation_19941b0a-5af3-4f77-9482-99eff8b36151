# RayBoxUI

RayBoxUI 是一款集成了 x-ui、3x-ui、s-ui 的掌上节点管理配置工具，专为移动端优化设计。

## 功能特性

- 🔧 **统一管理** - 支持 x-ui、3x-ui、s-ui 多种面板类型
- 📱 **移动优化** - 专为移动设备设计的响应式界面
- ⚡ **快速配置** - 简化的配置流程，一键生成配置文件
- 📱 **二维码生成** - 自动生成二维码，方便客户端导入
- 🔒 **安全可靠** - 支持证书指纹验证，确保连接安全
- 🌙 **深色模式** - 支持明暗主题切换
- 📄 **隐私保护** - 所有数据本地存储，不上传到服务器

## 支持的面板类型

### s-ui
- 使用 API 密钥进行认证
- 简化的界面设计

### x-ui
- 使用用户名/密码认证
- 原版 x-ui 面板支持

### 3x-ui
- 使用用户名/密码认证
- 增强功能和更好性能

## 技术栈

- **框架**: Next.js 15 with App Router
- **UI 组件**: shadcn/ui
- **样式**: Tailwind CSS
- **图标**: Lucide React
- **二维码**: react-qr-code
- **语言**: TypeScript

## 快速开始

### 安装依赖

```bash
npm install
```

### 启动开发服务器

```bash
npm run dev
```

打开 [http://localhost:3000](http://localhost:3000) 查看应用。

### 构建生产版本

```bash
npm run build
npm start
```

## 使用说明

### 1. 创建配置

1. 访问首页，点击"配置节点"按钮
2. 选择面板类型（s-ui、x-ui、3x-ui）
3. 填写配置信息：
   - 配置名称
   - 完整URL（包含协议、域名/IP、端口）
   - 认证信息（用户名/密码 或 API密钥）
   - 证书指纹（可选，用于安全验证）

### 2. 生成配置

- 点击"生成配置"按钮
- 查看生成的 JSON 配置
- 扫描二维码或下载配置文件

### 3. 配置格式

生成的配置文件包含以下信息：

```json
{
  "type": "s-ui",
  "name": "我的节点",
  "host": "example.com",
  "port": 8080,
  "protocol": "https",
  "api": "your-api-key",
  "certFingerprints": ["sha256-fingerprint"]
}
```

## 项目结构

```
raybox-ui/
├── app/                    # Next.js App Router
│   ├── config/            # 配置页面
│   ├── privacy/           # 隐私条款页面
│   ├── layout.tsx         # 根布局
│   └── page.tsx           # 首页
├── components/            # React 组件
│   └── ui/               # shadcn/ui 组件
├── lib/                   # 工具函数和类型
│   ├── types.ts          # TypeScript 类型定义
│   ├── url-utils.ts      # URL 解析工具
│   └── utils.ts          # 通用工具函数
└── public/               # 静态资源
```

## 开发指南

### 添加新的面板类型

1. 在 `lib/types.ts` 中添加新的接口定义
2. 更新 `UIConfig` 联合类型
3. 在配置表单中添加相应的字段
4. 更新配置生成逻辑

### 自定义样式

项目使用 Tailwind CSS，可以在 `app/globals.css` 中添加自定义样式。

## 部署

### Vercel 部署

1. 将代码推送到 GitHub
2. 在 Vercel 中导入项目
3. 自动部署完成

### 其他平台

```bash
npm run build
```

将 `out` 目录部署到任何静态托管平台。

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request！
