export type ProtocolType = 'https' | 'http';

export interface BaseConfig {
  name: string;
  url: string
}

export interface SUIConfig extends BaseConfig {
  type: 's-ui';
  api: string;
  protocol: ProtocolType;
  certFingerprints?: string[]; // 处理后的证书SHA256指纹数组
}

export interface XUIConfig extends BaseConfig {
  type: 'x-ui';
  username: string;
  password: string;
  protocol: ProtocolType;
  certFingerprints?: string[]; // 处理后的证书SHA256指纹数组
}

export interface ThreeXUIConfig extends BaseConfig {
  type: '3x-ui';
  username: string;
  password: string;
  protocol: ProtocolType;
  certFingerprints?: string[]; // 处理后的证书SHA256指纹数组
}

export type UIConfig = SUIConfig | XUIConfig | ThreeXUIConfig;
