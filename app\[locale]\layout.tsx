import { ReactNode } from 'react';
import { I18nProvider } from '@/components/i18n-provider';
import { Locale, locales } from '@/i18n';

export function generateStaticParams() {
  return locales.map((locale) => ({ locale }));
}

type Props = {
  children: ReactNode;
  params: Promise<{ locale: string }>;
};

export default async function LocaleLayout({
  children,
  params
}: Props) {
  const { locale } = await params;

  return (
    <I18nProvider locale={locale as Locale}>
      {children}
    </I18nProvider>
  );
}
