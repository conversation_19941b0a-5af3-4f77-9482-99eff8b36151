{"common": {"appName": "RayBoxUI", "startConfig": "开始配置", "learnMore": "了解更多", "configNode": "配置节点", "privacyPolicy": "隐私条款"}, "home": {"title": "掌上节点管理工具", "subtitle": "集成了 x-ui、3x-ui、s-ui 的统一管理平台，让节点配置变得简单高效", "features": {"unified": {"title": "统一管理", "description": "支持 x-ui、3x-ui、s-ui 多种面板，一个应用管理所有节点"}, "mobile": {"title": "移动优化", "description": "专为移动设备优化，随时随地管理您的节点配置"}, "quick": {"title": "快速配置", "description": "简化配置流程，生成二维码，快速导入到客户端"}, "secure": {"title": "安全可靠", "description": "支持证书指纹验证，确保连接安全性"}}}, "config": {"title": "节点配置", "description": "创建和管理您的节点配置，支持 s-ui、x-ui、3x-ui 多种面板类型", "form": {"createConfig": "创建配置", "panelType": "面板类型", "configName": "配置名称", "configNamePlaceholder": "输入配置名称", "fullUrl": "完整URL", "fullUrlPlaceholder": "https://example.com:8080", "fullUrlHint": "包含协议、域名/IP和端口", "username": "用户名", "usernamePlaceholder": "输入用户名", "password": "密码", "passwordPlaceholder": "输入密码", "apiKey": "API密钥", "apiKeyPlaceholder": "输入API密钥", "certFingerprints": "证书指纹 (可选)", "certFingerprintsPlaceholder": "每行一个SHA256指纹", "certFingerprintsHint": "用于验证服务器证书，每行一个", "generateConfig": "生成配置", "inDevelopment": "开发中"}, "errors": {"nameRequired": "请输入配置名称", "urlRequired": "请输入完整URL", "urlInvalid": "URL格式错误", "usernameRequired": "请输入用户名", "passwordRequired": "请输入密码", "apiKeyRequired": "请输入API密钥", "configGenerationFailed": "配置生成失败"}}, "footer": {"copyright": "© 2025 RayBoxUI. 专为节点管理而生。"}}