"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Copy, Download, Eye, EyeOff } from "lucide-react";
import QRCode from "react-qr-code";
import { UIConfig } from "@/lib/types";

interface ConfigPreviewProps {
  config: UIConfig;
  onDownload: () => void;
}

export function ConfigPreview({ config, onDownload }: ConfigPreviewProps) {
  const [showQR, setShowQR] = useState(true);
  const [showSensitive, setShowSensitive] = useState(false);
  const [copied, setCopied] = useState(false);

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(JSON.stringify(config, null, 2));
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error("Failed to copy:", error);
    }
  };

  const maskSensitiveData = (config: UIConfig) => {
    const masked = { ...config };
    
    if ('password' in masked && masked.password) {
      masked.password = showSensitive ? masked.password : '••••••••';
    }
    
    if ('api' in masked && masked.api) {
      masked.api = showSensitive ? masked.api : '••••••••';
    }

    return masked;
  };

  const displayConfig = maskSensitiveData(config);



  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          配置预览
          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowSensitive(!showSensitive)}
            >
              {showSensitive ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
            </Button>
            <Button variant="outline" size="sm" onClick={handleCopy}>
              <Copy className="w-4 h-4 mr-2" />
              {copied ? "已复制" : "复制"}
            </Button>
            <Button variant="outline" size="sm" onClick={onDownload}>
              <Download className="w-4 h-4 mr-2" />
              下载
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* QR Code Toggle */}
        <div className="flex justify-center">
          <Button
            variant="outline"
            onClick={() => setShowQR(!showQR)}
            className="mb-4"
          >
            {showQR ? "隐藏" : "显示"}二维码
          </Button>
        </div>

        {/* QR Code */}
        {showQR && (
          <div className="flex justify-center p-4 bg-white rounded-lg border">
            <QRCode
              value={JSON.stringify(config)}
              size={200}
              level="M"
              className="max-w-full h-auto"
            />
          </div>
        )}

        {/* Config JSON */}
        <div className="bg-gray-100 dark:bg-gray-800 rounded-lg p-4 overflow-hidden">
          <pre className="text-sm overflow-x-auto whitespace-pre-wrap break-words">
            {JSON.stringify(displayConfig, null, 2)}
          </pre>
        </div>

        {/* Config Summary */}
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="font-medium text-gray-600 dark:text-gray-400">类型:</span>
            <span className="ml-2 capitalize">{config.type}</span>
          </div>
          <div>
            <span className="font-medium text-gray-600 dark:text-gray-400">协议:</span>
            <span className="ml-2 uppercase">{config.protocol}</span>
          </div>
          <div className="col-span-2">
            <span className="font-medium text-gray-600 dark:text-gray-400">URL:</span>
            <span className="ml-2 break-all">{config.url}</span>
          </div>
        </div>

        {config.certFingerprints && config.certFingerprints.length > 0 && (
          <div className="text-sm">
            <span className="font-medium text-gray-600 dark:text-gray-400">证书指纹:</span>
            <div className="mt-1 space-y-1">
              {config.certFingerprints.map((fp, index) => (
                <div key={index} className="font-mono text-xs bg-gray-50 dark:bg-gray-700 p-2 rounded">
                  {fp}
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
