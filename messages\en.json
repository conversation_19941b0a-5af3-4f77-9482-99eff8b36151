{"common": {"appName": "RayBoxUI", "startConfig": "Start Configuration", "learnMore": "Learn More", "configNode": "Configure Node", "privacyPolicy": "Privacy Policy"}, "home": {"title": "Mobile Node Management Tool", "subtitle": "Unified management platform integrating x-ui, 3x-ui, s-ui, making node configuration simple and efficient", "features": {"unified": {"title": "Unified Management", "description": "Support x-ui, 3x-ui, s-ui panels, manage all nodes with one application"}, "mobile": {"title": "Mobile Optimized", "description": "Optimized for mobile devices, manage your node configuration anytime, anywhere"}, "quick": {"title": "Quick Configuration", "description": "Simplified configuration process, generate QR codes, quickly import to clients"}, "secure": {"title": "Secure & Reliable", "description": "Support certificate fingerprint verification to ensure connection security"}}}, "config": {"title": "Node Configuration", "description": "Create and manage your node configurations, supporting s-ui, x-ui, 3x-ui panel types", "form": {"createConfig": "Create Configuration", "panelType": "Panel Type", "configName": "Configuration Name", "configNamePlaceholder": "Enter configuration name", "fullUrl": "Full URL", "fullUrlPlaceholder": "https://example.com:8080", "fullUrlHint": "Include protocol, domain/IP and port", "username": "Username", "usernamePlaceholder": "Enter username", "password": "Password", "passwordPlaceholder": "Enter password", "apiKey": "API Key", "apiKeyPlaceholder": "Enter API key", "certFingerprints": "Certificate Fingerprints (Optional)", "certFingerprintsPlaceholder": "One SHA256 fingerprint per line", "certFingerprintsHint": "For server certificate verification, one per line", "generateConfig": "Generate Configuration", "inDevelopment": "In Development"}, "errors": {"nameRequired": "Please enter configuration name", "urlRequired": "Please enter full URL", "urlInvalid": "Invalid URL format", "usernameRequired": "Please enter username", "passwordRequired": "Please enter password", "apiKeyRequired": "Please enter API key", "configGenerationFailed": "Configuration generation failed"}}, "footer": {"copyright": "© 2025 RayBoxUI. Built for node management."}}