import { parseUrl, buildUrl } from '../url-utils';

describe('URL Utils', () => {
  describe('parseUrl', () => {
    it('should parse HTTPS URL correctly', () => {
      const result = parseUrl('https://example.com:8080/path');
      expect(result).toEqual({
        protocol: 'https',
        host: 'example.com',
        port: 8080,
        path: '/path'
      });
    });

    it('should parse HTTP URL correctly', () => {
      const result = parseUrl('http://***********:3000');
      expect(result).toEqual({
        protocol: 'http',
        host: '***********',
        port: 3000,
        path: undefined
      });
    });

    it('should use default ports when not specified', () => {
      const httpsResult = parseUrl('https://example.com');
      expect(httpsResult.port).toBe(443);

      const httpResult = parseUrl('http://example.com');
      expect(httpResult.port).toBe(80);
    });

    it('should throw error for invalid protocol', () => {
      expect(() => parseUrl('ftp://example.com')).toThrow('协议必须是 http 或 https');
    });

    it('should throw error for invalid URL', () => {
      expect(() => parseUrl('not-a-url')).toThrow('无效的URL格式');
    });
  });

  describe('buildUrl', () => {
    it('should build HTTPS URL correctly', () => {
      const result = buildUrl('https', 'example.com', 8080, '/api');
      expect(result).toBe('https://example.com:8080/api');
    });

    it('should build HTTP URL correctly', () => {
      const result = buildUrl('http', 'localhost', 3000);
      expect(result).toBe('http://localhost:3000');
    });

    it('should omit default ports', () => {
      const httpsResult = buildUrl('https', 'example.com', 443);
      expect(httpsResult).toBe('https://example.com');

      const httpResult = buildUrl('http', 'example.com', 80);
      expect(httpResult).toBe('http://example.com');
    });
  });
});
